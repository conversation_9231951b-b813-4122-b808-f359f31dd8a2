package com.stpl.tech.kettle.crm.util;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum DroolFileType {

    LOY<PERSON>TY_SCREEN_DECISION("loyalty_screen_decision"), CRM_SCREEN_FLOW_DECISION("crm_screen_flow_decision"),
    CUSTOMER_RECOM_DROOL_DECISION("customer_recom_drool_decision"),

    CUSTOMER_ENGAGEMENT_DROOL_DECISION("customer_engagement_drool_decision"),
    FREE_PRODUCT_DROOL_DECISION("free_product_drool_decision"),
    MEMBERSHIP_SUGGESTION_DROOL_DECISION("membership_suggestion_drool_decision"),
    CART_RULES_DROOL_DECISION("cart_rules_drool_decision"),
    OUTPUT_RULES_DROOL_DECISION("output_rules_drool_decision"),
    RULES_DROOL_DECISION("rules_drool_decision");
    private String fileName;


}
