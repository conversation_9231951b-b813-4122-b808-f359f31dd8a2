package com.stpl.tech.kettle.crm.service;

import com.stpl.tech.kettle.crm.data.kettle.DroolsDecisionTableData;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.data.util.Pair;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface DroolsDecisionService {


//    public boolean resetOfferDecisionDrools(MultipartFile file, String droolFileType, boolean persist);
    boolean addNewDroolFile(MultipartFile file,String droolFileType, boolean persist);

    public void downloadRecipeMedia(HttpServletResponse response, String fileName, String droolFileType, String version) throws IOException;

    boolean activateVersion(String fileName, String droolFileType, String version) throws IOException;

    List<DroolsDecisionTableData> fetchAllFileByType(String fileType);

    public boolean isDroolContainerInitializeForCustomerFlow(String version);

    public boolean isDroolContainerInitializeForLoyaltyScreens(String version);

    abstract boolean isDroolContainerInitializeForCustomerEngagement(String version);

    abstract boolean isDroolContainerInitializeForMembership(String version);

    public void initailizeDroolContainer(String droolFileType, String version);

    boolean isDroolContainerInitializeForCustomerRecommendations(String version);

    boolean isDroolContainerInitializeForFreeProduct(String version);

    boolean isDroolContainerInitializeForCartRules(String version);

    boolean isDroolContainerInitializeForOutputRules(String version);

    boolean setDefaultDroolSheet(String droolFileType, String version, String fileName);
    boolean inactivateVersion(String droolFileType, String version, String fileName);

}
