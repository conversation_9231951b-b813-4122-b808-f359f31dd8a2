package com.stpl.tech.kettle.crm.data.kettle;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static jakarta.persistence.GenerationType.IDENTITY;


@NoArgsConstructor
@AllArgsConstructor
@Entity
@Getter
@Setter
@Table(name = "ORDER_DETAIL", uniqueConstraints = @UniqueConstraint(columnNames = "GENERATED_ORDER_ID"))
public class OrderDetail implements java.io.Serializable {
    private static final long serialVersionUID = 5441681397850434848L;


    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ORDER_ID", unique = true, nullable = false)
    private Integer orderId;

    @Column(name = "GENERATED_ORDER_ID", unique = true, nullable = false, length = 30)
    private String generatedOrderId;

    @Column(name = "CUSTOMER_ID")
    private Integer customerId;

    @Column(name = "CUSTOMER_NAME")
    private String customerName;

    @Column(name = "ORDER_SOURCE", nullable = false, length = 10)
    private String orderSource;

    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;

    @Temporal(TemporalType.DATE)
    @Column(name = "BUSINESS_DATE", nullable = true, length = 10)
    private Date businessDate;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "BILLING_SERVER_TIME", nullable = false, length = 19)
    private Date billingServerTime;

    @Column(name="ORDER_STATUS", nullable = false, length = 30)
    private String orderStatus;

    @Column(name = "IS_GIFT_CARD_ORDER", nullable = true)
    private String isGiftCardOrder;

    @Column(name = "BRAND_ID")
    private Integer brandId;

    @OneToMany(fetch = FetchType.EAGER, mappedBy = "orderDetail")
    private List<OrderItem> orderItems = new ArrayList<OrderItem>(0);

}
