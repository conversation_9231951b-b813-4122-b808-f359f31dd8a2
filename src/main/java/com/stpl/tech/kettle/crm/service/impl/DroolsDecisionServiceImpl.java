package com.stpl.tech.kettle.crm.service.impl;

import com.stpl.tech.kettle.crm.data.kettle.DroolsDecisionTableData;
import com.stpl.tech.kettle.crm.drools.DroolConfig;
import com.stpl.tech.kettle.crm.repository.kettle.DroolsDecisionTableDataDao;
import com.stpl.tech.kettle.crm.service.DroolsDecisionService;
import com.stpl.tech.kettle.crm.util.AppConstants;
import com.stpl.tech.kettle.crm.util.AppUtils;
import com.stpl.tech.kettle.crm.util.DroolFileType;
import com.stpl.tech.kettle.crm.util.FileArchiveService;
import com.stpl.tech.kettle.crm.util.FileDetail;
import jakarta.persistence.NoResultException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class DroolsDecisionServiceImpl implements DroolsDecisionService {

    private static final String DROOLS_FOR_LOYALTY_SCREEN = "drools/loyalty_screen_decision";

    private static final String DROOLS_FOR_CRM_SCREEN = "drools/crm_screen_flow_decision";

    private static final String DROOLS_FOR_CUSTOMER_RECOM_DROOL_DECISION = "drools/customer_recom_drool_decision";

    private static final String DROOLS_FOR_CUSTOMER_ENGAGEMENT_DROOL_DECISION = "drools/customer_engagement_drool_decision";
    private static final String DROOLS_FOR_FREE_PRODUCT_DROOL_DECISION = "drools/free_product_drool_decision";
    private static final String DROOLS_FOR_MEMBERSHIP_DROOLS_DECISION = "/drools/membership_suggestion_drool_decision";
    @Autowired
    private DroolConfig droolConfig;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private EnvironmentProperties properties;

    @Autowired
    private DroolsDecisionTableDataDao droolsDecisionDao;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addNewDroolFile(MultipartFile file,String droolFileType, boolean persist){
        try {
            if(Objects.isNull(droolFileType)){
                log.info("Error while updating drool sheet as drool file type in null");
                return false;
            }
            String fileTypeName = DroolFileType.valueOf(droolFileType).getFileName();
            DroolsDecisionTableData currentDroolData = getDecisionTableData(droolFileType);
            if(Objects.isNull(currentDroolData)){
                currentDroolData = DroolsDecisionTableData.builder().fileName(fileTypeName + "_v0.0.xls").build();
            }
            String fileName = getNextVersion(currentDroolData.getFileName().split(".xls")[0]) + ".xls";
            String version = "v" + fileName.split("_v")[1].split(".xls")[0];
            if(persist){
                log.info("Drools Base directory :::::::::",properties.getEnvironmentType().name().toLowerCase() + "/" + fileTypeName);
                String baseDir = properties.getEnvironmentType().name().toLowerCase() + "/" + fileTypeName + "/" + version;
                log.info(":::::: Request to upload New Drool decision table ::::::");
                FileDetail s3File = fileArchiveService.saveFileToS3(properties.getS3BucketForDrools(),
                        baseDir, fileName, file, true);
                if (s3File != null) {
                    log.info("URL ::::: {}", s3File.getUrl());
                }
                saveCurrentAndDeactivateOther(DroolsDecisionTableData.builder()
                        .type(DroolFileType.valueOf(droolFileType).name())
                        .creationTime(AppUtils.getCurrentTimestamp())
                        .fileName(fileName)
                        .status(AppConstants.PROCESSING)
                        .version(version)
                        .isDefault(AppConstants.NO)
                        .build());
            }
        }catch (Exception e){
            return false;
        }
        return true;
    }


    private void resetDroolContainer(String droolFileType, String version) {
        if (DroolFileType.LOYALTY_SCREEN_DECISION.equals(DroolFileType.valueOf(droolFileType))) {
            droolConfig.initDroolConfigForLoyaltyScreens(version);
        }
        if (DroolFileType.CRM_SCREEN_FLOW_DECISION.equals(DroolFileType.valueOf(droolFileType))) {
            droolConfig.initDroolConfigForCustomerFlow(version);
        }
        if (DroolFileType.CUSTOMER_RECOM_DROOL_DECISION.equals(DroolFileType.valueOf(droolFileType))) {
            droolConfig.initDroolConfigForCustomerRecommendationDecision(version);
        }
        if (DroolFileType.CUSTOMER_ENGAGEMENT_DROOL_DECISION.equals(DroolFileType.valueOf(droolFileType))) {
            droolConfig.initDroolConfigForCustomerEngagementDroolDecision(version);
        }
        if (DroolFileType.FREE_PRODUCT_DROOL_DECISION.equals(DroolFileType.valueOf(droolFileType))) {
            droolConfig.initDroolConfigForFreeProductDroolDecision(version);
        }
        if(DroolFileType.MEMBERSHIP_SUGGESTION_DROOL_DECISION.equals(DroolFileType.valueOf(droolFileType))){
            droolConfig.initDroolConfigMembershipSuggestionDroolDecision(version);
        }
        if(DroolFileType.CART_RULES_DROOL_DECISION.equals(DroolFileType.valueOf(droolFileType))){
            droolConfig.initDroolConfigForCartRulesDroolDecision(version);
        }
        if(DroolFileType.OUTPUT_RULES_DROOL_DECISION.equals(DroolFileType.valueOf(droolFileType))){
            droolConfig.initDroolConfigForOutputRulesDroolDecision(version);
        }

    }

    @Override
    public void downloadRecipeMedia(HttpServletResponse response, String fileName, String droolFileType, String version) throws IOException {
        String s3RecipeMediaBucket = properties.getS3BucketForDrools();
        String envType = properties.getEnvironmentType().name().toLowerCase();
        FileDetail fileDetail = new FileDetail(s3RecipeMediaBucket, envType + "/" + DroolFileType.valueOf(droolFileType).getFileName() + "/" + version + "/" + fileName, null);
        File file = fileArchiveService.getFileFromS3(properties.getDroolBasePath() + File.separator + "s3", fileDetail);
        setFileToResponse(response, fileName, file);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager",readOnly = false, propagation = Propagation.REQUIRED)
    public boolean activateVersion(String fileName, String droolFileType, String version) throws IOException {
        try {
            if(Objects.isNull(droolFileType)){
                log.info("Error while updating drool sheet as drool file type in null");
                return false;
            }
            getFileFromS3andDownload(DroolFileType.valueOf(droolFileType).getFileName(),fileName,version);
            resetDroolContainer(droolFileType, version);
//            deactivateOther(droolFileType);
            activateCurrentFile(droolFileType, fileName,version);
        }catch (Exception e ){
            return false;
        }
        return true;
    }

    @Override
    public List<DroolsDecisionTableData> fetchAllFileByType(String fileType) {
        try {
            List<DroolsDecisionTableData> data = droolsDecisionDao.findByTypeOrderById(fileType);
            List<DroolsDecisionTableData> result = new ArrayList<>();
            if (!CollectionUtils.isEmpty(data)) {
                for(DroolsDecisionTableData e : data){
                    if(!AppConstants.DECLINE.equals(e.getStatus())){
                        result.add(e);
                    }
                }
                return result;
            }
        } catch (NoResultException e) {
            log.error("No decision table data found for type : {}", fileType);
        } catch (Exception e) {
            log.error("Exception occurred while fetching decision table data for type : {} ", fileType);
        }
        return new ArrayList<>();
    }

    private void setFileToResponse(HttpServletResponse response, String fileName, File file) throws IOException {
        if (Objects.nonNull(file)) {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.addHeader("Content-Disposition", "attachment; filename=" + file.getName());
            byte[] bytesArray = new byte[(int) file.length()];
            response.setContentLength(bytesArray.length);
            try {
                OutputStream outputStream = response.getOutputStream();
                InputStream inputStream = new FileInputStream(file);
                int counter = 0;
                while ((counter = inputStream.read(bytesArray, 0, bytesArray.length)) > 0) {
                    outputStream.write(bytesArray, 0, counter);
                    outputStream.flush();
                }
                outputStream.close();
                inputStream.close();
            } catch (IOException e) {
                log.error("Encountered error while writing file to response stream", e);
                throw e;
            } finally {
                response.getOutputStream().flush();
                file.delete(); // delete the temporary file created after completing request
            }
        }
    }

    private static String getNextVersion(String fileName) {
        Double version = Double.valueOf(fileName.split("_v")[1]);
        version = version + .1;
        version = BigDecimal.valueOf(version).setScale(1, RoundingMode.HALF_UP).doubleValue();
        return (fileName.split("_v")[0] + "_v" + version);
    }

    private void saveCurrentAndDeactivateOther(DroolsDecisionTableData data) {
        //deactivateOther(data.getType());
        declineExistingProcessingFile(data.getType(),data.getFileName());
        droolsDecisionDao.save(data);
    }

    public DroolsDecisionTableData getDecisionTableData(String type) {
        try {
            DroolsDecisionTableData data = droolsDecisionDao.findTop1ByTypeAndStatusOrderByIdDesc(type,AppConstants.ACTIVE);
            if (Objects.nonNull(data)) {
                return data;
            }
        } catch (NoResultException e) {
            log.error("No decision table data found for type : {} and status :{}", type);
        } catch (Exception e) {
            log.error("Exception occurred while fetching decision table data for type : {}", type);
        }
        return null;
    }


    public void deactivateOther(String type) {
        try {
            DroolsDecisionTableData droolsData = droolsDecisionDao.findByTypeAndStatus(type , AppConstants.ACTIVE);
            if(Objects.nonNull(droolsData)) {
                droolsData.setStatus(AppConstants.IN_ACTIVE);
                droolsDecisionDao.save(droolsData);
            }
        } catch (Exception e) {
            log.error("Exception occurred while deactivating other decision table data for type : {} ", type);
        }
    }

    public void activateCurrentFile(String type, String fileName, String version) {
        try {
            DroolsDecisionTableData droolsData = droolsDecisionDao.findByTypeAndFileNameAndVersion(type , fileName, version);
            if(Objects.nonNull(droolsData)) {
                droolsData.setStatus(AppConstants.ACTIVE);
                droolsDecisionDao.save(droolsData);
            }
        } catch (Exception e) {
            log.error("Exception occurred while activating decision table data for type : {} and fileName : {} ", type, fileName);
        }
    }

    public void declineExistingProcessingFile(String droolFileType,String fileName){
        try {
            droolsDecisionDao.declineExistingProcessingFile(droolFileType,AppConstants.DECLINE,AppConstants.PROCESSING,fileName);
        }catch (Exception e){
            log.error("Exception occurred while change status of existing Drool file for type : {} and fileName : {} with error : {}",droolFileType,fileName,e.getMessage());
        }
    }

    @Override
    public boolean isDroolContainerInitializeForLoyaltyScreens(String version){
        return Objects.nonNull(droolConfig.getKieContainerForLoyaltyScreenFlowDecision(version));
    }

    @Override
    public boolean isDroolContainerInitializeForCustomerFlow(String version){
        return Objects.nonNull(droolConfig.getKieContainerForCustomerFlowDecision(version));
    }

    @Override
    public boolean isDroolContainerInitializeForCustomerRecommendations(String version) {
        return  Objects.nonNull(droolConfig.getKieContainerForCustomerRecomDroolDecision(version));
    }

    @Override
    public boolean isDroolContainerInitializeForCustomerEngagement(String version) {
        return  Objects.nonNull(droolConfig.getKieContainerForCustomerEngagementDroolDecision(version));
    }

    @Override
    public boolean isDroolContainerInitializeForMembership(String version) {
        return  Objects.nonNull(droolConfig.getKieContainerForMembershipSuggestionDroolDecision(version));
    }

    @Override
    public boolean isDroolContainerInitializeForFreeProduct(String version) {
        return  Objects.nonNull(droolConfig.getKieContainerForFreeProductDroolDecision(version));
    }

    @Override
    public boolean isDroolContainerInitializeForCartRules(String version) {
        return  Objects.nonNull(droolConfig.getKieContainerForCartRulesDroolDecision(version));
    }

    @Override
    public boolean isDroolContainerInitializeForOutputRules(String version) {
        return  Objects.nonNull(droolConfig.getKieContainerForOutputRulesDroolDecision(version));
    }

    public void processAndDownloadDroolFile(String droolFileType, String version) throws Exception {
        String fileTypeName = droolFileType;
        try {
            List<String> statuses = Arrays.asList(AppConstants.ACTIVE, AppConstants.PROCESSING);
            List<DroolsDecisionTableData> currentDroolData = droolsDecisionDao.findByTypeAndStatusIn(droolFileType.toUpperCase(),statuses);
            boolean isFileUpdated = false;
            if (Objects.nonNull(currentDroolData) && !currentDroolData.isEmpty()) {
                for (DroolsDecisionTableData data : currentDroolData) {
                    if (AppConstants.PROCESSING.equals(data.getStatus()) && !isFileUpdated) {
                        getFileFromS3andDownload(fileTypeName, data.getFileName(),version);
                        declineExistingProcessingFile(droolFileType.toUpperCase(),data.getFileName());
//                        deactivateOther(droolFileType.toUpperCase());
                        activateCurrentFile(droolFileType.toUpperCase(), data.getFileName(),version);
                        isFileUpdated = true;
                    }
                }
                if(!isFileUpdated){
                    DroolsDecisionTableData data = currentDroolData.get(0);
                    File file = null;
                    if(AppConstants.ACTIVE.equals(data.getStatus())) {
                        String filePath = Objects.nonNull(version) ? properties.getDroolBasePath() + "/drools/" + fileTypeName + "/" + version + "/" + fileTypeName + ".xls" :
                                properties.getDroolBasePath() + "/drools/" + fileTypeName + "/default/" + fileTypeName + ".xls";
                        file = Paths.get(filePath).toFile();
                    }
                    if(Objects.isNull(file) || !file.exists()){
                        getFileFromS3andDownload(fileTypeName, data.getFileName(),version);
                    }
                }
            }else{
                copyFromResource(droolFileType);
            }
        }catch (Exception e){
            log.info("Error in Processing Drool file : {}",e.getMessage());
            log.info("Drool File not available on S3");
        }
    }

    @Override
    public void initailizeDroolContainer(String droolFileType, String version){
        try{
            processAndDownloadDroolFile(droolFileType,version);
            if(DroolFileType.CRM_SCREEN_FLOW_DECISION.getFileName().equals(droolFileType)) {
                droolConfig.initDroolConfigForCustomerFlow(version);
            }
            if(DroolFileType.LOYALTY_SCREEN_DECISION.getFileName().equals(droolFileType)) {
                droolConfig.initDroolConfigForLoyaltyScreens(version);
            }
            if(DroolFileType.CUSTOMER_RECOM_DROOL_DECISION.getFileName().equals(droolFileType)) {
                droolConfig.initDroolConfigForCustomerRecommendationDecision(version);
            }
            if(DroolFileType.CUSTOMER_ENGAGEMENT_DROOL_DECISION.getFileName().equals(droolFileType)) {
                droolConfig.initDroolConfigForCustomerEngagementDroolDecision(version);
            }
            if(DroolFileType.FREE_PRODUCT_DROOL_DECISION.getFileName().equals(droolFileType)) {
                droolConfig.initDroolConfigForFreeProductDroolDecision(version);
            }
            if(DroolFileType.MEMBERSHIP_SUGGESTION_DROOL_DECISION.getFileName().equals(droolFileType)) {
                droolConfig.initDroolConfigMembershipSuggestionDroolDecision(version);
            }
            if(DroolFileType.CART_RULES_DROOL_DECISION.getFileName().equals(droolFileType)) {
                droolConfig.initDroolConfigForCartRulesDroolDecision(version);
            }
            if(DroolFileType.OUTPUT_RULES_DROOL_DECISION.getFileName().equals(droolFileType)) {
                droolConfig.initDroolConfigForOutputRulesDroolDecision(version);
            }
        }catch (Exception e){
            log.info("Error in initialize drool container for offerDecision : {}",e);
        }
    }

    private void getFileFromS3andDownload(String fileTypeName,String fileName,String version) throws Exception {
        OutputStream os;
        String s3RecipeMediaBucket = properties.getS3BucketForDrools();
        String envType = properties.getEnvironmentType().name().toLowerCase();
        String path = properties.getDroolBasePath() + "/drools/" + fileTypeName + "/";
        String filePath = Objects.nonNull(version) ? envType + "/" + fileTypeName + "/" + version + "/" + fileName :
                envType + "/" + fileTypeName + "/default/" + fileTypeName;
        FileDetail fileDetail = new FileDetail(s3RecipeMediaBucket, filePath, null);
        File file = fileArchiveService.getFileFromS3(properties.getDroolBasePath() + File.separator + "s3", fileDetail);
        FileInputStream input = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile("file",
                file.getName(), "text/plain", IOUtils.toByteArray(input));
        String newFilePath = Objects.nonNull(version) ? path + version + "/" + fileTypeName + ".xls" : path + "default/" + fileTypeName + ".xls";
        File newFile = new File(newFilePath);
        if (!newFile.exists()) {
            Path dirPath = Paths.get(path + (Objects.nonNull(version) ? version : "default"));
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
            }
            boolean isFileCreated = newFile.createNewFile();
            if (!isFileCreated) {
                log.info("Unable to create file {} skipping reset process", newFile.getAbsolutePath());
                throw new Exception("Unable to Create file");
            }
        }
        os = new FileOutputStream(newFile);
        os.write(multipartFile.getBytes());
        os.close();
        os.flush();
    }

    public void copyFromResource(String droolFileType){
        String destinationDirectory = "";
        String sourceFileDirectory = "";
        if(DroolFileType.LOYALTY_SCREEN_DECISION.getFileName().equals(droolFileType)){
            destinationDirectory = properties.getDroolBasePath() + "/" + DROOLS_FOR_LOYALTY_SCREEN;
            sourceFileDirectory = DROOLS_FOR_LOYALTY_SCREEN + "/" + droolFileType + ".xls";
        }
        if(DroolFileType.CRM_SCREEN_FLOW_DECISION.getFileName().equals(droolFileType)){
            destinationDirectory = properties.getDroolBasePath() + "/" + DROOLS_FOR_CRM_SCREEN;
            sourceFileDirectory = DROOLS_FOR_CRM_SCREEN + "/" + droolFileType + ".xls";
        }
        if(DroolFileType.CUSTOMER_RECOM_DROOL_DECISION.getFileName().equals(droolFileType)){
            destinationDirectory = properties.getDroolBasePath() + "/" + DROOLS_FOR_CUSTOMER_RECOM_DROOL_DECISION;
            sourceFileDirectory = DROOLS_FOR_CUSTOMER_RECOM_DROOL_DECISION + "/" + droolFileType + ".xls";
        }
        if(DroolFileType.CUSTOMER_ENGAGEMENT_DROOL_DECISION.getFileName().equals(droolFileType)){
            destinationDirectory = properties.getDroolBasePath() + "/" + DROOLS_FOR_CUSTOMER_ENGAGEMENT_DROOL_DECISION;
            sourceFileDirectory = DROOLS_FOR_CUSTOMER_ENGAGEMENT_DROOL_DECISION + "/" + droolFileType + ".xls";
        }
        if(DroolFileType.FREE_PRODUCT_DROOL_DECISION.getFileName().equals(droolFileType)){
            destinationDirectory = properties.getDroolBasePath() + "/" + DROOLS_FOR_FREE_PRODUCT_DROOL_DECISION;
            sourceFileDirectory = DROOLS_FOR_FREE_PRODUCT_DROOL_DECISION + "/" + droolFileType + ".xls";
        }
        if(DroolFileType.MEMBERSHIP_SUGGESTION_DROOL_DECISION.getFileName().equals(droolFileType)){
            destinationDirectory = properties.getDroolBasePath() + "/" + DROOLS_FOR_MEMBERSHIP_DROOLS_DECISION;
            sourceFileDirectory = DROOLS_FOR_MEMBERSHIP_DROOLS_DECISION + "/" + droolFileType + ".xls";
        }
        try {
            fileArchiveService.saveFileToDestinationPath(sourceFileDirectory,destinationDirectory,droolFileType);
        }catch (Exception e){
            log.info("Error in saving drool file into data folder : {}",e.getMessage());
            log.error("Error in saving file",e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean setDefaultDroolSheet(String droolFileType,String fileName, String version) {
        try {
            String s3RecipeMediaBucket = properties.getS3BucketForDrools();
            String envType = properties.getEnvironmentType().name().toLowerCase();
            FileDetail fileDetail = new FileDetail(s3RecipeMediaBucket,envType + "/" + DroolFileType.valueOf(droolFileType).getFileName() + "/" + version + "/" + fileName, null);
            File file = fileArchiveService.getFileFromS3(properties.getDroolBasePath() + File.separator + "s3",fileDetail);
            try (FileInputStream input = new FileInputStream(file)) {
                MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), "text/plain", IOUtils.toByteArray(input));
                String filePath = properties.getDroolBasePath() + "/drools/" + DroolFileType.valueOf(droolFileType).getFileName() + "/default";
                Path dirPath = Paths.get(filePath);
                Path newFilePath = dirPath.resolve(DroolFileType.valueOf(droolFileType).getFileName() + ".xls");
                if (!Files.exists(dirPath)) {
                    Files.createDirectories(dirPath);
                }
                if (Files.deleteIfExists(newFilePath)) {
                    log.info("Existing file {} deleted successfully", newFilePath);
                }
                try (OutputStream os = new FileOutputStream(newFilePath.toFile())) {
                    os.write(multipartFile.getBytes());
                    log.info("New file created and written: {}", newFilePath);
                }
                String fileTypeName = DroolFileType.valueOf(droolFileType).getFileName();
                String baseDir = properties.getEnvironmentType().name().toLowerCase() + "/" + fileTypeName + "/default";
                log.info(":::::: Request to upload default file to S3 ::::::");
                FileDetail s3File = fileArchiveService.saveFileToS3(properties.getS3BucketForDrools(),
                        baseDir, DroolFileType.valueOf(droolFileType).getFileName() + ".xls", multipartFile, true);
                if (s3File != null) {
                    log.info("URL ::::: {}", s3File.getUrl());
                }
                setFileAsDefault(droolFileType, version);
                resetDroolContainer(droolFileType, null);
            } catch (IOException e) {
                log.error("Error processing the file: {}", e.getMessage());
                throw new RuntimeException("File processing failed", e);
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    public void setFileAsDefault(String droolFileType, String version) {
        droolsDecisionDao.setIsDefaultStatus(droolFileType.toUpperCase(), AppConstants.NO);
        DroolsDecisionTableData currentDroolData = droolsDecisionDao.findByTypeAndVersion(droolFileType.toUpperCase(), version);
        if (Objects.nonNull(currentDroolData)) {
            currentDroolData.setIsDefault(AppConstants.YES);
            droolsDecisionDao.save(currentDroolData);
        }
    }

    @Override
    public boolean inactivateVersion(String droolFileType, String fileName, String version) {
        try {
            String folderPath = properties.getEnvironmentType().name().toLowerCase() + "/" + DroolFileType.valueOf(droolFileType).getFileName() + "/" + version;
            Path filePath = Paths.get(folderPath);
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                log.info("File deleted from server: " + folderPath);
            } else {
                log.info("File does not exist on server: " + folderPath);
            }
            setFileStatus(droolFileType, version, AppConstants.IN_ACTIVE);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public void setFileStatus(String droolFileType, String version, String status) {
        DroolsDecisionTableData currentDroolData = droolsDecisionDao.findByTypeAndVersion(droolFileType.toUpperCase(), version);
        if (Objects.nonNull(currentDroolData)) {
            currentDroolData.setStatus(status);
            currentDroolData.setIsDefault(AppConstants.NO);
            droolsDecisionDao.save(currentDroolData);
        }
    }

}

