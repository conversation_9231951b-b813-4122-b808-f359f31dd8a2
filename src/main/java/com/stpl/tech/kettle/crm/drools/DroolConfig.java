package com.stpl.tech.kettle.crm.drools;

import lombok.extern.slf4j.Slf4j;
import org.drools.decisiontable.DecisionTableProviderImpl;
import org.kie.api.KieServices;
import org.kie.api.builder.KieBuilder;
import org.kie.api.builder.KieFileSystem;
import org.kie.api.builder.KieModule;
import org.kie.api.io.Resource;
import org.kie.api.runtime.KieContainer;
import org.kie.internal.io.ResourceFactory;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Configuration
@Slf4j
public class DroolConfig {
    private static final String SEPARATOR = "/";
    private static final String CRM_SCREEN_FLOW_DROOL_FILE = "crm_screen_flow_decision.xls";
    private static final String CRM_SCREEN_FLOW_DROOL_PATH = "/drools/crm_screen_flow_decision/";

    private static final String LOYALTY_SECTION_DROOL_FILE = "loyalty_screen_decision.xls";
    private static final String LOYALTY_SECTION_DROOL_PATH = "/drools/loyalty_screen_decision/";

    private static final String CUSTOMER_RECOM_DROOL_FILE = "customer_recom_drool_decision.xls";
    private static final String CUSTOMER_RECOM_DROOL_PATH = "/drools/customer_recom_drool_decision/";

    private static final String CUSTOMER_ENGAGEMENT_DROOL_FILE = "customer_engagement_drool_decision.xls";
    private static final String CUSTOMER_ENGAGEMENT_DROOL_PATH = "/drools/customer_engagement_drool_decision/";
    private static final String FREE_PRODUCT_DECISION_DROOL_FILE = "free_product_drool_decision.xls";
    private static final String FREE_PRODUCT_DECISION_DROOL_PATH = "/drools/free_product_drool_decision/";
    private static final String MEMBERSHIP_SUGGESTION_DROOL_PATH = "/drools/membership_suggestion_drool_decision";
    private static final String MEMBERSHIP_SUGGESTION_DROOL_FILE =  "membership_suggestion_drool_decision.xls";
    private static final String CART_RULES_DROOL_FILE = "cart_rules_drool_decision.xls";
    private static final String CART_RULES_DROOL_PATH = "/drools/cart_rules_drool_decision/";
    private static final String OUTPUT_RULES_DROOL_FILE = "output_rules_drool_decision.xls";
    private static final String OUTPUT_RULES_DROOL_PATH = "/drools/output_rules_drool_decision/";
    private static final KieServices kieServices = KieServices.Factory.get();
    private Map<String,KieContainer> kieContainerForLoyaltyScreenFlowDecision = new ConcurrentHashMap<>();
    private Map<String,KieContainer> kieContainerForCustomerFlowDecision = new ConcurrentHashMap<>();
    private Map<String,KieContainer> kieContainerForCustomerRecomDroolDecision = new ConcurrentHashMap<>();

    private Map<String,KieContainer> kieContainerForCustomerEngagementDroolDecision = new ConcurrentHashMap<>();
    private Map<String,KieContainer> kieContainerForFreeProductDroolDecision = new ConcurrentHashMap<>();
    private Map<String,KieContainer> kieContainerForMembershipSuggestionDroolDecision = new ConcurrentHashMap<>();
    private Map<String,KieContainer> kieContainerForCartRulesDroolDecision = new ConcurrentHashMap<>();
    private Map<String,KieContainer> kieContainerForOutputRulesDroolDecision = new ConcurrentHashMap<>();

    public KieContainer getKieContainerForCustomerFlowDecision(String version) {
        return this.kieContainerForCustomerFlowDecision.get(Objects.nonNull(version) ? version : "default");
    }

    public KieContainer getKieContainerForLoyaltyScreenFlowDecision(String version) {
        return this.kieContainerForLoyaltyScreenFlowDecision.get(Objects.nonNull(version) ? version : "default");
    }


    public KieContainer getKieContainerForCustomerRecomDroolDecision(String version) {
        return this.kieContainerForCustomerRecomDroolDecision.get(Objects.nonNull(version) ? version : "default");
    }

    public KieContainer getKieContainerForCustomerEngagementDroolDecision(String version) {
        return this.kieContainerForCustomerEngagementDroolDecision.get(Objects.nonNull(version) ? version : "default");
    }

    public KieContainer getKieContainerForFreeProductDroolDecision(String version) {
        return this.kieContainerForFreeProductDroolDecision.get(Objects.nonNull(version) ? version : "default");
    }

    public KieContainer getKieContainerForMembershipSuggestionDroolDecision(String version) {
        return this.kieContainerForMembershipSuggestionDroolDecision.get(Objects.nonNull(version) ? version : "default");
    }

    public KieContainer getKieContainerForCartRulesDroolDecision(String version) {
        return this.kieContainerForCartRulesDroolDecision.get(Objects.nonNull(version) ? version : "default");
    }

    public KieContainer getKieContainerForOutputRulesDroolDecision(String version) {
        return this.kieContainerForOutputRulesDroolDecision.get(Objects.nonNull(version) ? version : "default");
    }

    public void initDroolConfigForCustomerFlow(String version) {
        try {
            if(Objects.isNull(version)){
                throw new Exception();
            }
            else{
                log.info("Version received for getting drools file {} and version {}",CRM_SCREEN_FLOW_DROOL_FILE,version);
                initializeCrmScreenFlowDrool(version);
            }
        } catch (Exception e) {
            log.info("Getting default version");
            initializeCrmScreenFlowDrool("default");
        }
    }

    public void initializeCrmScreenFlowDrool(String version){
        String baseDir = "/data/app/kettle/" + System.getProperty("env.type") + CRM_SCREEN_FLOW_DROOL_PATH + version + SEPARATOR;
        String filePath = baseDir + CRM_SCREEN_FLOW_DROOL_FILE;
        Path dirPath = Paths.get(baseDir);
        if (!Files.exists(dirPath)) {
            try {
                Files.createDirectories(dirPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        Resource dt = ResourceFactory.newUrlResource("file:" + filePath);
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem().write(dt);
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();
        KieModule kieModule = kieBuilder.getKieModule();
        kieContainerForCustomerFlowDecision.put(version,kieServices.newKieContainer(kieModule.getReleaseId()));
    }

    public void initDroolConfigForLoyaltyScreens(String version) {
        try {
            if(Objects.isNull(version)){
                throw new Exception();
            }
            else{
                log.info("Version received for getting drools file {} and version {}",LOYALTY_SECTION_DROOL_FILE,version);
                initializeLoyaltyScreenDrool(version);
            }
        } catch (Exception e) {
            log.info("Getting default version");
            initializeLoyaltyScreenDrool("default");
        }
    }

    public void initializeLoyaltyScreenDrool(String version){
        String baseDir = "/data/app/kettle/" + System.getProperty("env.type") + LOYALTY_SECTION_DROOL_PATH + version + SEPARATOR;
        String filePath = baseDir + LOYALTY_SECTION_DROOL_FILE;
        Path dirPath = Paths.get(baseDir);
        if (!Files.exists(dirPath)) {
            try {
                Files.createDirectories(dirPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        Resource dt = ResourceFactory.newUrlResource("file:" + filePath);
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem().write(dt);
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();
        KieModule kieModule = kieBuilder.getKieModule();
        kieContainerForLoyaltyScreenFlowDecision.put(version,kieServices.newKieContainer(kieModule.getReleaseId()));
    }

    public void initDroolConfigForCustomerRecommendationDecision(String version) {
        try {
            if(Objects.isNull(version)){
                throw new Exception();
            }
            else{
                log.info("Version received for getting drools file {} and version {}",CUSTOMER_RECOM_DROOL_FILE,version);
                initializeCustomerRecomDrool(version);
            }

        } catch (Exception e) {
            log.info("Getting default version");
            initializeCustomerRecomDrool("default");
        }

    }

    public void initializeCustomerRecomDrool(String version){
        String baseDir = "/data/app/kettle/" + System.getProperty("env.type") + CUSTOMER_RECOM_DROOL_PATH + version + SEPARATOR;
        String filePath = baseDir + CUSTOMER_RECOM_DROOL_FILE;
        Path dirPath = Paths.get(baseDir);
        if (!Files.exists(dirPath)) {
            try {
                Files.createDirectories(dirPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        Resource dt = ResourceFactory.newUrlResource("file:" + filePath);
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem().write(dt);
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();
        KieModule kieModule = kieBuilder.getKieModule();
        kieContainerForCustomerRecomDroolDecision.put(version,kieServices.newKieContainer(kieModule.getReleaseId()));
    }

    public void initDroolConfigForCustomerEngagementDroolDecision(String version) {
        try {
            if(Objects.isNull(version)){
                throw new Exception();
            }
            else{
                log.info("Version received for getting drools file {} and version {}",CUSTOMER_ENGAGEMENT_DROOL_FILE,version);
                initializeCustomerEngagementDrool(version);
            }
        } catch (Exception e) {
            log.info("Getting default version");
            initializeCustomerEngagementDrool("default");
        }
    }

    public void initializeCustomerEngagementDrool(String version){
        String baseDir = "/data/app/kettle/" + System.getProperty("env.type") + CUSTOMER_ENGAGEMENT_DROOL_PATH + version + SEPARATOR;
        String filePath = baseDir + CUSTOMER_ENGAGEMENT_DROOL_FILE;
        Path dirPath = Paths.get(baseDir);
        if (!Files.exists(dirPath)) {
            try {
                Files.createDirectories(dirPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        Resource dt = ResourceFactory.newUrlResource("file:" + filePath);
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem().write(dt);
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();
        KieModule kieModule = kieBuilder.getKieModule();
        kieContainerForCustomerEngagementDroolDecision.put(version, kieServices.newKieContainer(kieModule.getReleaseId()));
    }

    public void initDroolConfigForFreeProductDroolDecision(String version) {
        try {
            if(Objects.isNull(version)){
                throw new Exception();
            }
            else{
                log.info("Version received for getting drools file {} and version {}",FREE_PRODUCT_DECISION_DROOL_FILE,version);
                initializeFreeProductDrool(version);
            }
        } catch (Exception e) {
            initializeFreeProductDrool("default");
        }
    }

    public void initializeFreeProductDrool(String version){
        String baseDir = "/data/app/kettle/" + System.getProperty("env.type") + FREE_PRODUCT_DECISION_DROOL_PATH + version + SEPARATOR;
        String filePath = baseDir + FREE_PRODUCT_DECISION_DROOL_FILE;
        Path dirPath = Paths.get(baseDir);
        if (!Files.exists(dirPath)) {
            try {
                Files.createDirectories(dirPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        Resource dt = ResourceFactory.newUrlResource("file:" + filePath);
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem().write(dt);
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();
        KieModule kieModule = kieBuilder.getKieModule();
        kieContainerForFreeProductDroolDecision.put(version, kieServices.newKieContainer(kieModule.getReleaseId()));
    }

    public void initDroolConfigMembershipSuggestionDroolDecision(String version) {
        try {
            if(Objects.isNull(version)){
                throw new Exception();
            }
            else{
                log.info("Version received for getting drools file {} and version {}",FREE_PRODUCT_DECISION_DROOL_FILE,version);
                initializeMembershipSuggestionDrool(version);
            }
        } catch (Exception e) {
            initializeMembershipSuggestionDrool("default");
        }
    }

    public void initializeMembershipSuggestionDrool(String version){
        String baseDir = "/data/app/kettle/" + System.getProperty("env.type") + MEMBERSHIP_SUGGESTION_DROOL_PATH + SEPARATOR + version + SEPARATOR;
        String filePath = baseDir + MEMBERSHIP_SUGGESTION_DROOL_FILE;
        Path dirPath = Paths.get(baseDir);
        if (!Files.exists(dirPath)) {
            try {
                Files.createDirectories(dirPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        Resource dt = ResourceFactory.newUrlResource("file:" + filePath);
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem().write(dt);
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();
        KieModule kieModule = kieBuilder.getKieModule();
        kieContainerForMembershipSuggestionDroolDecision.put(version, kieServices.newKieContainer(kieModule.getReleaseId()));
    }

    public void initDroolConfigForCartRulesDroolDecision(String version) {
        try {
            if(Objects.isNull(version)){
                throw new Exception();
            }
            else{
                log.info("Version received for getting drools file {} and version {}",CART_RULES_DROOL_FILE,version);
                initializeCartRulesDrool(version);
            }
        } catch (Exception e) {
            log.info("Getting default version");
            initializeCartRulesDrool("default");
        }
    }

    public void initializeCartRulesDrool(String version){
        String baseDir = "/data/app/kettle/" + System.getProperty("env.type") + CART_RULES_DROOL_PATH + version + SEPARATOR;
        String filePath = baseDir + CART_RULES_DROOL_FILE;
        Path dirPath = Paths.get(baseDir);
        if (!Files.exists(dirPath)) {
            try {
                Files.createDirectories(dirPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        Resource dt = ResourceFactory.newUrlResource("file:" + filePath);
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem().write(dt);
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();
        KieModule kieModule = kieBuilder.getKieModule();
        kieContainerForCartRulesDroolDecision.put(version, kieServices.newKieContainer(kieModule.getReleaseId()));
    }

    public void initDroolConfigForOutputRulesDroolDecision(String version) {
        try {
            if(Objects.isNull(version)){
                throw new Exception();
            }
            else{
                log.info("Version received for getting drools file {} and version {}",OUTPUT_RULES_DROOL_FILE,version);
                initializeOutputRulesDrool(version);
            }
        } catch (Exception e) {
            log.info("Getting default version");
            initializeOutputRulesDrool("default");
        }
    }

    public void initializeOutputRulesDrool(String version){
        String baseDir = "/data/app/kettle/" + System.getProperty("env.type") + OUTPUT_RULES_DROOL_PATH + version + SEPARATOR;
        String filePath = baseDir + OUTPUT_RULES_DROOL_FILE;
        Path dirPath = Paths.get(baseDir);
        if (!Files.exists(dirPath)) {
            try {
                Files.createDirectories(dirPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        Resource dt = ResourceFactory.newUrlResource("file:" + filePath);
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem().write(dt);
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();
        KieModule kieModule = kieBuilder.getKieModule();
        kieContainerForOutputRulesDroolDecision.put(version, kieServices.newKieContainer(kieModule.getReleaseId()));
    }
}
